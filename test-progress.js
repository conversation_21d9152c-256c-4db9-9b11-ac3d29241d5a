// Simple test script to verify progress tracking functionality
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/aegisGrader';
const TEST_JOB_ID = 'test-job-' + Date.now();

async function testProgressTracking() {
    console.log('Testing progress tracking with jobId:', TEST_JOB_ID);
    
    try {
        // 1. Create initial progress record
        console.log('\n1. Creating initial progress record...');
        const createResponse = await axios.post(`${BASE_URL}/test-progress/${TEST_JOB_ID}`, {
            progress: 0
        });
        console.log('✓ Initial progress created:', createResponse.data);
        
        // 2. Test progress updates
        console.log('\n2. Testing progress updates...');
        const progressValues = [10, 25, 50, 75, 90, 100];
        
        for (const progress of progressValues) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
            
            const updateResponse = await axios.post(`${BASE_URL}/test-progress/${TEST_JOB_ID}`, {
                progress: progress
            });
            console.log(`✓ Progress updated to ${progress}%:`, updateResponse.data);
        }
        
        console.log('\n✅ Progress tracking test completed successfully!');
        console.log(`\nTo test the frontend, use jobId: ${TEST_JOB_ID}`);
        console.log(`You can also test the SSE endpoint directly: ${BASE_URL}/getCurrentProgress/${TEST_JOB_ID}`);
        
    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

// Run the test
testProgressTracking();
