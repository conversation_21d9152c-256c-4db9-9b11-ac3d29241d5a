<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progress Tracking Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AegisGrader Progress Tracking Test</h1>
        
        <div>
            <label for="jobId">Job ID:</label>
            <input type="text" id="jobId" placeholder="Enter job ID or use test job" />
            <button onclick="useTestJob()">Use Test Job</button>
        </div>
        
        <div>
            <button onclick="connect()" id="connectBtn">Connect</button>
            <button onclick="disconnect()" id="disconnectBtn" disabled>Disconnect</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div>
            <strong>Progress: <span id="progressText">0%</span></strong>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
        
        <div>
            <h3>Event Log:</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let currentJobId = null;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusDiv.textContent = `Connected to job: ${currentJobId}`;
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function updateProgress(progress) {
            const progressText = document.getElementById('progressText');
            const progressFill = document.getElementById('progressFill');
            
            progressText.textContent = `${progress}%`;
            progressFill.style.width = `${progress}%`;
        }

        function useTestJob() {
            const jobIdInput = document.getElementById('jobId');
            jobIdInput.value = 'test-job-' + Date.now();
        }

        function connect() {
            const jobId = document.getElementById('jobId').value.trim();
            if (!jobId) {
                alert('Please enter a job ID');
                return;
            }

            if (eventSource) {
                eventSource.close();
            }

            currentJobId = jobId;
            log(`Connecting to job: ${jobId}`);

            eventSource = new EventSource(`http://localhost:5000/api/aegisGrader/getCurrentProgress/${jobId}`);

            eventSource.onopen = function(event) {
                log('✓ Connection opened');
                updateStatus(true);
            };

            eventSource.onmessage = function(event) {
                try {
                    const progress = JSON.parse(event.data);
                    log(`📊 Progress update: ${progress}%`);
                    updateProgress(progress);
                } catch (error) {
                    log(`❌ Error parsing progress data: ${error.message}`);
                }
            };

            eventSource.onerror = function(event) {
                log('❌ Connection error occurred');
                updateStatus(false);
                
                // Attempt to reconnect after 5 seconds
                setTimeout(() => {
                    if (eventSource && eventSource.readyState === EventSource.CLOSED) {
                        log('🔄 Attempting to reconnect...');
                        connect();
                    }
                }, 5000);
            };
        }

        function disconnect() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                log('🔌 Disconnected');
                updateStatus(false);
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Initialize
        log('Ready to test progress tracking');
    </script>
</body>
</html>
