import { getAllSubmissions, getPresignedUrl, getCurrentProgress, handleProcessingResults, processRefunds, processLambdaRefunds } from "../controllers/aegisGraderController.js";
import { verifyJWT } from "../middleware/verifyJWT.js";
import { checkAegisGraderCredits } from "../middleware/creditCheck.js";

import express from "express";

const router = express.Router();

// Legacy route (commented out since submitForGrading is no longer used)
// router.post("/submit", checkAegisGraderCredits, submitForGrading);
router.get("/submissions/:userId", getAllSubmissions);

// Apply credit check to presigned URL generation (new SQS/Lambda workflow)
router.post("/getPresigned", checkAegisGraderCredits, getPresignedUrl);

router.get("/getCurrentProgress/:jobId", getCurrentProgress); // for polling the current progress for a particular jobid

// Test endpoint to simulate progress updates (for development/testing)
router.post("/test-progress/:jobId", async (req, res) => {
    try {
        const { jobId } = req.params;
        const { progress } = req.body;

        const PollTime = (await import("../models/PollTime.js")).default;

        await PollTime.findOneAndUpdate(
            { jobId },
            { currentProgress: progress, timestamp: new Date() },
            { upsert: true, new: true }
        );

        res.json({ message: "Progress updated", jobId, progress });
    } catch (error) {
        console.error("Error updating test progress:", error);
        res.status(500).json({ error: error.message });
    }
});

// Handle processing results from Lambda function (no JWT required for Lambda callbacks)
router.post("/processing-results", handleProcessingResults);

// Simple refund endpoint for Lambda (no JWT required for Lambda callbacks)
router.post("/process-lambda-refunds", processLambdaRefunds);

// Process refunds for failed evaluations (requires authentication)
router.post("/refunds/:submissionId?", processRefunds);

export default router;
