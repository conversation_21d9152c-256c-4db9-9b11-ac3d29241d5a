# AegisGrader Real-time Progress Tracking Implementation

## Overview
I've implemented real-time progress tracking for AegisGrader submissions using Server-Sent Events (SSE) to show live updates on the grading details page.

## Components Implemented

### 1. Backend Changes

#### Models
- **AegisGrader.js**: Added `jobId` field to track processing jobs
- **PollTime.js**: Existing model for storing progress data (jobId, currentProgress, timestamp)

#### Controllers
- **aegisGraderController.js**:
  - `getCurrentProgress()`: SSE endpoint that streams real-time progress updates
  - Updated `getPresignedUrl()`: Creates initial progress record and includes jobId in response
  - Updated `getAllSubmissions()`: Returns jobId with submission data

#### Routes
- **aegisGraderRoutes.js**:
  - `GET /getCurrentProgress/:jobId`: SSE endpoint for progress streaming
  - `POST /test-progress/:jobId`: Test endpoint for simulating progress updates

### 2. Frontend Changes

#### Hooks
- **useProgressTracking.ts**: Custom hook for managing SSE connections and progress state

#### Components
- **ProgressTracker.tsx**: React component that displays real-time progress with:
  - Progress bar with shimmer animation
  - Connection status indicator
  - Error handling
  - Live status badge

#### Pages
- **GradingDetails.tsx**: Updated to include ProgressTracker component in the sidebar

#### Styles
- **index.css**: Added shimmer animation for progress bars

## How It Works

1. **Submission Creation**: When a user creates a submission via `getPresigned`, a `jobId` (manifestKey) is generated and stored in both the manifest and a PollTime record.

2. **Progress Updates**: Lambda function (or any processing service) updates progress by writing to the PollTime collection using the jobId.

3. **Real-time Display**: The grading details page connects to the SSE endpoint and receives live progress updates.

4. **Automatic Cleanup**: SSE connections are automatically cleaned up when components unmount or connections fail.

## Testing

### Prerequisites
1. Start the backend server: `cd backend && npm start`
2. Ensure MongoDB is running

### Test Files Created
1. **test-progress.js**: Node.js script to simulate progress updates
2. **test-progress.html**: HTML page to test SSE connections directly

### Testing Steps

#### 1. Test Backend Functionality
```bash
node test-progress.js
```
This will create a test job and simulate progress updates from 0% to 100%.

#### 2. Test SSE Connection
1. Open `test-progress.html` in a browser
2. Click "Use Test Job" to generate a test job ID
3. Click "Connect" to establish SSE connection
4. Use the Node.js script to update progress and see real-time updates

#### 3. Test in Application
1. Create an AegisGrader submission
2. Navigate to grading details page
3. The ProgressTracker should appear in the sidebar
4. Use the test endpoint to simulate progress updates:
   ```bash
   curl -X POST http://localhost:5000/api/aegisGrader/test-progress/YOUR_JOB_ID \
        -H "Content-Type: application/json" \
        -d '{"progress": 50}'
   ```

## Integration with Lambda

For the Lambda function to work with this system:

1. **Read jobId from manifest**: Lambda should extract the jobId from the uploaded manifest file
2. **Update progress**: Lambda should periodically update progress using:
   ```javascript
   await PollTime.findOneAndUpdate(
       { jobId: manifestJobId },
       { currentProgress: progressPercentage, timestamp: new Date() },
       { upsert: true }
   );
   ```
3. **Set jobId in AegisGrader document**: When Lambda creates the AegisGrader document, it should include the jobId field

## Features

- **Real-time Updates**: Progress updates appear instantly without page refresh
- **Connection Management**: Automatic reconnection on connection failures
- **Error Handling**: Graceful handling of connection errors and invalid data
- **Responsive Design**: Works on all screen sizes
- **Theme Compatible**: Supports light/dark themes
- **Performance Optimized**: Efficient SSE implementation with proper cleanup

## API Endpoints

- `GET /api/aegisGrader/getCurrentProgress/:jobId` - SSE endpoint for progress streaming
- `POST /api/aegisGrader/test-progress/:jobId` - Test endpoint for progress simulation
- `GET /api/aegisGrader/submissions/:userId` - Returns submissions with jobId

## Next Steps

1. **Lambda Integration**: Update Lambda function to use the jobId for progress tracking
2. **Error Recovery**: Implement progress recovery for failed jobs
3. **Progress Persistence**: Consider persisting progress data for longer periods
4. **Batch Updates**: Optimize for multiple concurrent jobs
5. **Monitoring**: Add logging and monitoring for progress tracking performance
