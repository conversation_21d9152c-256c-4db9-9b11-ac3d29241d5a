{"name": "aegis-scholar-frontend", "version": "0.1.0", "private": true, "dependencies": {"@dagrejs/dagre": "^1.1.4", "@heroicons/react": "^2.2.0", "@material-tailwind/react": "^2.1.10", "@pkasila/react-katex": "^1.2.2", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@react-oauth/google": "^0.12.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.8", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "@types/gsap": "^1.20.2", "@types/jest": "^27.5.2", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-modal": "^3.16.3", "@types/react-router-dom": "^5.3.3", "@visx/group": "^3.12.0", "@visx/heatmap": "^3.12.0", "@visx/scale": "^3.12.0", "@vitejs/plugin-react": "^4.3.4", "@xyflow/react": "^12.4.1", "axios": "^1.7.7", "better-react-mathjax": "^2.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "elkjs": "^0.9.3", "eslint": "^8.57.1", "fast-xml-parser": "^4.5.3", "framer-motion": "^12.4.2", "gsap": "^3.13.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "katex": "^0.16.22", "lucide-react": "^0.441.0", "ogl": "^1.0.11", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.3.93", "postprocessing": "^6.37.4", "radix-ui": "^1.4.2", "react": "^18.3.1", "react-day-picker": "^9.4.1", "react-dom": "^18.3.1", "react-intersection-observer": "^9.15.1", "react-joyride": "^2.9.3", "react-markdown": "^8.0.7", "react-modal": "^3.16.3", "react-player": "^2.16.0", "react-router-dom": "^6.26.1", "react-toastify": "^11.0.3", "recharts": "^2.12.7", "rehype-katex": "^6.0.3", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "testing-library": "^0.0.2", "three": "^0.177.0", "typescript": "^4.9.5", "typewriter-effect": "^2.21.0", "vis-data": "^7.1.9", "vis-network": "^9.1.9", "vite": "^6.1.1", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "vite --port 3000", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest run --coverage", "test:clean": "vitest run"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@tailwindcss/postcss": "^4.0.0", "@testing-library/dom": "^9.3.1", "@types/babel__generator": "^7.6.8", "@types/node": "^20.17.19", "@types/react-katex": "^3.0.4", "@types/three": "^0.177.0", "@vitest/coverage-v8": "^3.2.3", "jsdom": "^24.0.0", "postcss": "^8.4.31", "tailwindcss": "^4.0.0", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.2.3"}}