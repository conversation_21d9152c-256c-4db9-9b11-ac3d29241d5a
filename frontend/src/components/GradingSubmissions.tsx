import React, { useCallback, useEffect, useState } from 'react';
import { GradingStatus, TestSubmission } from '../types/aegisGrader';
import { useNavigate } from 'react-router-dom';
import { useAxiosPrivate } from "@/hooks/useAxiosPrivate";
import { fetchWithCache } from '@/utils/cacheUtil';
import { usePageRefresh } from '@/hooks/usePageRefresh';
import { useMobileInteractions } from '@/hooks/useMobileInteractions';
import { toast } from 'react-toastify';
import { AlertCircle } from 'lucide-react';
import { useUser } from '@/contexts/userContext';


// --- Helper Components --- (Using original naming if needed)
const StatusBadge: React.FC<{ status?: GradingStatus | string }> = React.memo(({ status = GradingStatus.PENDING }) => {
    const getStatusColor = useCallback(() => {
        // Handle both enum and string values
        const statusValue = typeof status === 'string' ? status.toUpperCase() : status;

        switch (statusValue) {
            case GradingStatus.COMPLETED:
            case 'COMPLETED':
                return 'bg-[hsl(var(--success)/0.2)] text-[hsl(var(--success))]';
            case GradingStatus.IN_PROGRESS:
            case 'IN_PROGRESS':
                return 'bg-[hsl(var(--info)/0.2)] text-[hsl(var(--info))]';
            case GradingStatus.FAILED:
            case 'FAILED':
                return 'bg-[hsl(var(--danger)/0.2)] text-[hsl(var(--danger))]';
            case GradingStatus.PENDING:
            case 'PENDING':
                return 'bg-[hsl(var(--warning)/0.2)] text-[hsl(var(--warning))]';
            default:
                return 'bg-[hsl(var(--muted-foreground)/0.2)] text-[hsl(var(--muted-foreground))]';
        }
    }, [status]);

    const displayStatus = typeof status === 'string' ? status.toLowerCase() : (status as string)?.toLowerCase() || 'pending';

    return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor()}`}>
            {displayStatus}
        </span>
    );
});

export const GradingSubmissions: React.FC<{ submissions: any[] }> = ({ submissions }) => {
    const navigate = useNavigate();
    const [testHistory, setTestHistory] = useState<TestSubmission[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const axiosPrivate = useAxiosPrivate();
    const { user } = useUser();
    // usePageRefresh();

    // Mobile interactions
    const { handleButtonPress } = useMobileInteractions({
        enableSwipe: true,
        swipeThreshold: 60
    });
    
    useEffect(() => {
            const fetchGradingSubmissions = async () => {
                try {
                    setIsLoading(true);
                    const data = await fetchWithCache(axiosPrivate, `/api/aegisGrader/submissions/${user?.id}`);
                    console.log("Fetched submissions:", data);
                    setTestHistory(data.submissions || []);

                    // if (data.submissions && data.submissions.length > 0) {
                    //     toast.success(`Loaded ${data.submissions.length} submission${data.submissions.length !== 1 ? 's' : ''}`);
                    // }
                } catch (error) {
                    console.error("Error fetching submissions:", error);
                    // toast.error("Failed to fetch submissions. Please try again.");
                } finally {
                    setIsLoading(false);
                }
            }
            fetchGradingSubmissions();
        }, [user]);
    

    const viewGradingDetails = useCallback((id: string | undefined) => {
            if (!id) {
                toast.error("Invalid submission ID");
                return;
            }

            const submission = testHistory.find(sub => sub.id === id);
            if (!submission) {
                toast.error("Submission not found");
                return;
            }

            // toast.info(`Opening details for ${submission.testDetails?.subject || 'submission'}...`);
            navigate(`/gradingDetails/` + id, {
                state: { testHistory },
            });
        }, [navigate, testHistory]);

    return (
        <div className="bg-card rounded-xl shadow-sm p-2 border border-border">
            <div className="flex flex-col gap-2">
                {testHistory.length === 0 ? (
                    <div className="text-center text-muted-foreground py-8 sm:py-12">
                        <div className="flex flex-col items-center gap-3">
                            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-muted rounded-full flex items-center justify-center">
                                <AlertCircle className="w-8 h-8 sm:w-10 sm:h-10 text-muted-foreground" />
                            </div>
                            <div>
                                <h3 className="text-base sm:text-lg font-medium text-foreground mb-1">No submissions yet</h3>
                                <p className="text-sm text-muted-foreground">Your test grading submissions will appear here</p>
                            </div>
                        </div>
                    </div>
                ) : (
                    testHistory.map((submission, index) => (
                        <div
                            key={index}
                            className="border rounded-xl p-4 sm:p-6 hover:bg-muted/50 cursor-pointer transition-all duration-200 hover:shadow-md touch-manipulation mobile-button"
                            onClick={handleButtonPress(() => viewGradingDetails(submission.id || ''))}
                        >
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 sm:gap-4 mb-3 sm:mb-4">
                                <div className="flex-1 min-w-0">
                                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-2">
                                        <h3 className="text-base sm:text-lg font-semibold text-foreground truncate">{submission.testDetails.subject}</h3>
                                        <StatusBadge status={submission.status || undefined} />
                                    </div>
                                    <p className="text-sm sm:text-base text-muted-foreground">
                                        {submission.testDetails.className} • {new Date(submission.testDetails.date).toLocaleDateString()}
                                    </p>
                                </div>
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                    <span className="bg-muted px-2 py-1 rounded-md">
                                        {submission.answerSheets.length} sheet{submission.answerSheets.length !== 1 ? 's' : ''}
                                    </span>
                                </div>
                            </div>
                            {submission.status === GradingStatus.IN_PROGRESS && submission.gradingProgress != null && (
                                <div className="mt-3 sm:mt-4">
                                    <div className="flex justify-between items-center mb-2">
                                        <span className="text-sm font-medium text-foreground">Grading Progress</span>
                                        <span className="text-sm text-muted-foreground">{submission.gradingProgress}%</span>
                                    </div>
                                    <div className="h-2 w-full bg-secondary rounded-full overflow-hidden">
                                        <div
                                            className="h-full bg-primary rounded-full transition-all duration-500"
                                            style={{ width: `${submission.gradingProgress}%` }}
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    ))
                )}
            </div>
        </div>
    );
}

export default GradingSubmissions;