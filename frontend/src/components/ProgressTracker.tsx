import React from 'react';
import { useProgressTracking } from '../hooks/useProgressTracking';
import { ClockIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface ProgressTrackerProps {
    jobId: string | null;
    submissionStatus: string;
    className?: string;
    showDetails?: boolean;
}

export const ProgressTracker: React.FC<ProgressTrackerProps> = ({
    jobId,
    submissionStatus,
    className = '',
    showDetails = true
}) => {
    const { progress, isConnected, error } = useProgressTracking(jobId, {
        enabled: submissionStatus === 'IN_PROGRESS' || submissionStatus === 'PENDING',
        onProgressUpdate: (progress) => {
            console.log('Progress updated:', progress);
        },
        onError: (error) => {
            console.error('Progress tracking error:', error);
        }
    });

    // Don't show progress tracker if submission is completed or failed
    if (submissionStatus === 'COMPLETED' || submissionStatus === 'FAILED') {
        return null;
    }

    // Don't show if no jobId is available
    if (!jobId) {
        return null;
    }

    const getStatusIcon = () => {
        if (error) {
            return <ExclamationTriangleIcon className="w-5 h-5 text-destructive" />;
        }
        if (submissionStatus === 'COMPLETED') {
            return <CheckCircleIcon className="w-5 h-5 text-success-600" />;
        }
        return <ClockIcon className="w-5 h-5 text-primary" />;
    };

    const getStatusText = () => {
        if (error) {
            return 'Connection Error';
        }
        if (submissionStatus === 'COMPLETED') {
            return 'Processing Complete';
        }
        if (submissionStatus === 'PENDING') {
            return 'Queued for Processing';
        }
        return 'Processing Answer Sheets';
    };

    const getProgressText = () => {
        if (error) {
            return 'Unable to track progress';
        }
        if (submissionStatus === 'PENDING') {
            return 'Waiting to start...';
        }
        if (progress === 0) {
            return 'Initializing...';
        }
        return `${Math.round(progress)}% complete`;
    };

    return (
        <div className={`bg-card rounded-lg border border-border p-4 ${className}`}>
            <div className="flex items-center gap-3 mb-3">
                {getStatusIcon()}
                <div className="flex-1">
                    <h3 className="font-semibold text-foreground text-sm">
                        {getStatusText()}
                    </h3>
                    {showDetails && (
                        <p className="text-xs text-muted-foreground">
                            {getProgressText()}
                        </p>
                    )}
                </div>
                {isConnected && !error && (
                    <div className="flex items-center gap-1">
                        <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
                        <span className="text-xs text-muted-foreground">Live</span>
                    </div>
                )}
            </div>

            {/* Progress Bar */}
            {(submissionStatus === 'IN_PROGRESS' || submissionStatus === 'PENDING') && !error && (
                <div className="space-y-2">
                    <div className="flex justify-between items-center">
                        <span className="text-xs font-medium text-foreground">Progress</span>
                        <span className="text-xs text-muted-foreground">
                            {Math.round(progress)}%
                        </span>
                    </div>
                    <div className="h-2 w-full bg-secondary rounded-full overflow-hidden">
                        <div
                            className="h-full bg-primary rounded-full transition-all duration-500 ease-out"
                            style={{ 
                                width: `${Math.min(100, Math.max(0, progress))}%`,
                                background: progress === 0 
                                    ? 'linear-gradient(90deg, #3b82f6 0%, #60a5fa 50%, #3b82f6 100%)'
                                    : undefined,
                                backgroundSize: progress === 0 ? '200% 100%' : undefined,
                                animation: progress === 0 ? 'shimmer 2s infinite' : undefined
                            }}
                        />
                    </div>
                </div>
            )}

            {/* Error State */}
            {error && (
                <div className="mt-2 p-2 bg-destructive/10 border border-destructive/20 rounded text-xs text-destructive">
                    {error.message}
                </div>
            )}

            {/* Connection Status */}
            {showDetails && jobId && (
                <div className="mt-3 pt-3 border-t border-border">
                    <div className="flex justify-between items-center text-xs text-muted-foreground">
                        <span>Job ID: {jobId.slice(-8)}</span>
                        <span className={`flex items-center gap-1 ${
                            isConnected ? 'text-success-600' : 'text-muted-foreground'
                        }`}>
                            <div className={`w-1.5 h-1.5 rounded-full ${
                                isConnected ? 'bg-success-500' : 'bg-muted-foreground'
                            }`}></div>
                            {isConnected ? 'Connected' : 'Disconnected'}
                        </span>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ProgressTracker;
