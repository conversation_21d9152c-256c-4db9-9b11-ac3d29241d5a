import React, { useState } from "react";
import {
    parseEvaluationData,
    validateEvaluationData,
    sanitizeNumber,
    sanitizeString,
    sanitizePercentage,
    type EvaluationBreakdown,
    type QuestionBreakdown,
    type CriterionBreakdown,
    type FeedbackSection
} from "@/utils/xmlEvaluationParser";

// --- Question Breakdown Function ---
export const parseQuestionBreakdown = parseEvaluationData;

// --- Structured Feedback Component ---
const StructuredFeedbackDisplay: React.FC<{
    feedback: string;
    structuredFeedback?: FeedbackSection[];
    questionNumber: string;
}> = ({ feedback, structuredFeedback, questionNumber }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set());

    const toggleSection = (index: number) => {
        const newExpanded = new Set(expandedSections);
        if (newExpanded.has(index)) {
            newExpanded.delete(index);
        } else {
            newExpanded.add(index);
        }
        setExpandedSections(newExpanded);
    };

    // If we have structured feedback, use it; otherwise fall back to plain text
    if (structuredFeedback && structuredFeedback.length > 0) {
        return (
            <div className="space-y-4">
                {structuredFeedback.map((section, index) => (
                    <div key={index} className="border border-border rounded-lg overflow-hidden">
                        <button
                            onClick={() => toggleSection(index)}
                            className="w-full p-3 bg-muted/20 hover:bg-muted/30 transition-colors text-left flex items-center justify-between"
                        >
                            <h5 className="font-medium text-foreground">{section.title}</h5>
                            <span className="text-muted-foreground text-sm">
                                {expandedSections.has(index) ? '−' : '+'}
                            </span>
                        </button>

                        {expandedSections.has(index) && (
                            <div className="p-3 bg-card border-t border-border">
                                <p className="text-sm text-foreground mb-3 leading-relaxed">
                                    {section.content}
                                </p>

                                {section.subsections && section.subsections.length > 0 && (
                                    <div className="space-y-3">
                                        {section.subsections.map((subsection, subIndex) => (
                                            <div key={subIndex} className="pl-4 border-l-2 border-primary/20">
                                                <h6 className="font-medium text-sm text-foreground mb-1">
                                                    {subsection.title}
                                                </h6>
                                                <p className="text-sm text-muted-foreground leading-relaxed">
                                                    {subsection.content}
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                ))}
            </div>
        );
    }

    // Fallback to plain text with expand/collapse for long feedback
    const isLongFeedback = feedback.length > 500;
    const displayText = isLongFeedback && !isExpanded
        ? `${feedback.substring(0, 500)}...`
        : feedback;

    return (
        <div className="space-y-2">
            <div className="p-3 bg-muted/30 border border-border rounded">
                <p className="text-sm text-foreground leading-relaxed break-words whitespace-pre-wrap">
                    {displayText}
                </p>
            </div>

            {isLongFeedback && (
                <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="text-xs text-primary hover:underline transition-colors"
                >
                    {isExpanded ? 'Show less' : 'Show full feedback'}
                </button>
            )}
        </div>
    );
};

// Criteria parsing is now handled by xmlEvaluationParser

// DOM parsing is now handled by xmlEvaluationParser

// --- Display Component for Question Breakdown ---
const QuestionBreakdownDisplay: React.FC<{ evaluationData: EvaluationBreakdown | null }> = ({ evaluationData }) => {
    // Handle null or invalid data
    if (!evaluationData) {
        return (
            <div className="bg-card border border-border rounded-lg p-6 text-center">
                <p className="text-muted-foreground">Unable to load question breakdown data.</p>
                <p className="text-xs text-muted-foreground mt-2">
                    Please check if the evaluation data is properly formatted.
                </p>
            </div>
        );
    }

    // Handle empty questions array
    if (!evaluationData.questions || evaluationData.questions.length === 0) {
        return (
            <div className="bg-card border border-border rounded-lg p-6 text-center">
                <p className="text-muted-foreground">No question breakdown available.</p>
                <p className="text-xs text-muted-foreground mt-2">
                    The evaluation may not contain detailed question-wise analysis.
                </p>
            </div>
        );
    }
    const getPerformanceColor = (percentage: number): string => {
        if (percentage >= 80) return "text-primary";
        if (percentage >= 60) return "text-foreground";
        if (percentage >= 40) return "text-muted-foreground";
        return "text-destructive";
    };

    const getPerformanceBg = (percentage: number): string => {
        if (percentage >= 80) return "bg-primary/5 border-primary/20";
        if (percentage >= 60) return "bg-accent/30 border-border";
        if (percentage >= 40) return "bg-muted/50 border-border";
        return "bg-destructive/5 border-destructive/20";
    };

    const getPerformanceBadge = (percentage: number): string => {
        if (percentage >= 80) return "bg-primary/10 text-primary border-primary/20";
        if (percentage >= 60) return "bg-accent text-foreground border-border";
        if (percentage >= 40) return "bg-muted text-muted-foreground border-border";
        return "bg-destructive/10 text-destructive border-destructive/20";
    };

    return (
        <div className="space-y-4 lg:space-y-6 p-2 lg:p-0">
            {/* Overall Performance */}
            {/* <div className="bg-card border border-border rounded-lg p-6">
                <h2 className="text-lg font-semibold mb-4 text-foreground">Overall Performance</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-1">Total Score</p>
                        <p className="text-2xl font-semibold text-foreground">
                            {evaluationData.totalMarks}/{evaluationData.maxMarks}
                        </p>
                    </div>
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-1">Percentage</p>
                        <p className={`text-2xl font-semibold ${getPerformanceColor(evaluationData.overallPercentage)}`}>
                            {evaluationData.overallPercentage}%
                        </p>
                    </div>
                    <div className="text-center p-4 bg-muted/30 rounded-lg">
                        <p className="text-sm text-muted-foreground mb-1">Questions</p>
                        <p className="text-2xl font-semibold text-foreground">{evaluationData.questions.length}</p>
                    </div>
                </div>
            </div> */}

            {/* Individual Question Breakdown */}
            <div className="space-y-3 lg:space-y-4">
                {/* <h2 className="text-lg font-semibold text-foreground">Question-wise Breakdown</h2> */}
                {evaluationData.questions.map((question) => (
                    <div key={question.questionNumber} className="bg-card border border-border rounded-lg overflow-hidden">
                        {/* Question Header */}
                        <div className={`p-3 lg:p-4 border-b ${getPerformanceBg(question.percentage)}`}>
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0">
                                <div className="flex items-center gap-2 lg:gap-3">
                                    <h3 className="text-sm lg:text-base font-semibold text-foreground">
                                        {question.questionNumber}
                                    </h3>
                                    <span className={`px-2 py-1 rounded text-xs font-medium border ${getPerformanceBadge(question.percentage)}`}>
                                        {question.percentage}%
                                    </span>
                                </div>
                                <div className="text-left sm:text-right">
                                    <p className={`text-sm lg:text-base font-semibold ${getPerformanceColor(question.percentage)}`}>
                                        {question.marksAwarded}/{question.marksPossible}
                                    </p>
                                    <p className="text-xs text-muted-foreground">marks</p>
                                </div>
                            </div>
                        </div>

                        {/* Question Content */}
                        <div className="p-3 lg:p-4 space-y-3 lg:space-y-4">
                            {/* Criteria Breakdown */}
                            {question.criteriaBreakdown.length > 0 && (
                                <div>
                                    <h4 className="text-sm font-medium text-foreground mb-2">Marking Criteria</h4>
                                    <div className="space-y-2">
                                        {question.criteriaBreakdown.map((criterion, criterionIndex) => {
                                            // Robust percentage calculation with validation
                                            const score = sanitizeNumber(criterion.score);
                                            const maxScore = sanitizeNumber(criterion.maxScore);
                                            const criterionPercentage = sanitizePercentage(score, maxScore);

                                            // Handle empty or invalid criterion names
                                            const criterionName = sanitizeString(criterion.criterion) || `Criterion ${criterionIndex + 1}`;

                                            // Check if maxScore is available and valid
                                            const hasValidMaxScore = criterion.maxScore && criterion.maxScore !== '0' && criterion.maxScore !== 'undefined' && criterion.maxScore !== '?';

                                            return (
                                                <div key={criterionIndex} className="flex flex-col gap-2 p-3 bg-muted/30 rounded border border-border">
                                                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2">
                                                        <span className="text-sm text-foreground flex-1 leading-relaxed" title={criterionName}>
                                                            {criterionName}
                                                        </span>
                                                        <div className="flex items-center gap-2 justify-start sm:justify-end flex-shrink-0">
                                                            <span className="text-sm font-medium text-foreground">
                                                                {hasValidMaxScore ? `${score}/${maxScore}` : score}
                                                            </span>
                                                            {hasValidMaxScore && maxScore > 0 && (
                                                                <span className={`text-xs px-2 py-1 rounded font-medium ${getPerformanceBadge(criterionPercentage)}`}>
                                                                    {criterionPercentage}%
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                </div>
                            )}

                            {/* Feedback */}
                            {question.feedback && (
                                <div>
                                    <h4 className="text-sm font-medium text-foreground mb-2">Detailed Feedback</h4>
                                    <StructuredFeedbackDisplay
                                        feedback={question.feedback}
                                        structuredFeedback={question.structuredFeedback}
                                        questionNumber={question.questionNumber}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

// --- Error Boundary Component ---
class QuestionBreakdownErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean; error?: Error }
> {
    constructor(props: { children: React.ReactNode }) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('QuestionBreakdown Error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="bg-card border border-destructive/20 rounded-lg p-6 text-center">
                    <p className="text-destructive font-medium mb-2">Error displaying question breakdown</p>
                    <p className="text-sm text-muted-foreground">
                        There was an issue processing the evaluation data. Please try refreshing the page.
                    </p>
                    <button
                        className="mt-3 px-4 py-2 bg-primary text-primary-foreground rounded text-sm hover:bg-primary/90"
                        onClick={() => this.setState({ hasError: false })}
                    >
                        Try Again
                    </button>
                </div>
            );
        }

        return this.props.children;
    }
}

// --- Main Export with Error Boundary ---
const QuestionBreakdownWithErrorBoundary: React.FC<{ evaluationData: EvaluationBreakdown | null }> = ({ evaluationData }) => {
    return (
        <QuestionBreakdownErrorBoundary>
            <QuestionBreakdownDisplay evaluationData={evaluationData} />
        </QuestionBreakdownErrorBoundary>
    );
};

// validateEvaluationData is now imported from xmlEvaluationParser

export default QuestionBreakdownWithErrorBoundary;
