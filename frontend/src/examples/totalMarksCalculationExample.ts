/**
 * Example demonstrating how to use the total marks calculation functions
 * to ensure accurate mark calculations and prevent calculation mistakes
 */

import {
    parseEvaluationData,
    calculateTotalMarksFromQuestions,
    calculateTotalPossibleMarksFromQuestions,
    validateAndCorrectEvaluationTotals,
    type EvaluationBreakdown,
    type QuestionBreakdown
} from '../utils/xmlEvaluationParser';

// Example 1: Calculate total marks from individual questions
export function exampleCalculateTotalMarks() {
    const questions: QuestionBreakdown[] = [
        {
            questionNumber: '1',
            marksAwarded: 18,
            marksPossible: 20,
            percentage: 90,
            feedback: 'Excellent work',
            criteriaBreakdown: []
        },
        {
            questionNumber: '2',
            marksAwarded: 15,
            marksPossible: 25,
            percentage: 60,
            feedback: 'Good effort, needs improvement',
            criteriaBreakdown: []
        },
        {
            questionNumber: '3',
            marksAwarded: 22,
            marksPossible: 25,
            percentage: 88,
            feedback: 'Very good analysis',
            criteriaBreakdown: []
        }
    ];

    // Calculate totals using the helper functions
    const totalMarks = calculateTotalMarksFromQuestions(questions);
    const totalPossible = calculateTotalPossibleMarksFromQuestions(questions);
    const percentage = Math.round((totalMarks / totalPossible) * 100);

    console.log('Manual Calculation Results:');
    console.log(`Total Marks: ${totalMarks}/${totalPossible} (${percentage}%)`);
    console.log(`Question 1: ${questions[0].marksAwarded}/${questions[0].marksPossible}`);
    console.log(`Question 2: ${questions[1].marksAwarded}/${questions[1].marksPossible}`);
    console.log(`Question 3: ${questions[2].marksAwarded}/${questions[2].marksPossible}`);
    
    return { totalMarks, totalPossible, percentage };
}

// Example 2: Validate and correct evaluation data
export function exampleValidateEvaluationData() {
    // Simulate evaluation data that might have calculation errors
    const evaluationWithErrors: EvaluationBreakdown = {
        totalMarks: 50, // This is incorrect (should be 55)
        maxMarks: 65,   // This is incorrect (should be 70)
        overallPercentage: 77, // This will be recalculated
        questions: [
            {
                questionNumber: '1',
                marksAwarded: 18,
                marksPossible: 20,
                percentage: 90,
                feedback: 'Excellent work',
                criteriaBreakdown: []
            },
            {
                questionNumber: '2',
                marksAwarded: 15,
                marksPossible: 25,
                percentage: 60,
                feedback: 'Good effort',
                criteriaBreakdown: []
            },
            {
                questionNumber: '3',
                marksAwarded: 22,
                marksPossible: 25,
                percentage: 88,
                feedback: 'Very good',
                criteriaBreakdown: []
            }
        ]
    };

    console.log('\nBefore Validation:');
    console.log(`Total: ${evaluationWithErrors.totalMarks}/${evaluationWithErrors.maxMarks} (${evaluationWithErrors.overallPercentage}%)`);

    // Validate and correct the totals
    const correctedEvaluation = validateAndCorrectEvaluationTotals(evaluationWithErrors);

    console.log('\nAfter Validation:');
    console.log(`Total: ${correctedEvaluation.totalMarks}/${correctedEvaluation.maxMarks} (${correctedEvaluation.overallPercentage}%)`);

    return correctedEvaluation;
}

// Example 3: Parse XML and automatically validate totals
export function exampleParseAndValidateXML() {
    const sampleXML = [
        `<evaluation>
            <total_marks_awarded>50</total_marks_awarded>
            <maximum_possible_marks>65</maximum_possible_marks>
            <question number="1">
                <marks_awarded>18</marks_awarded>
                <marks_possible>20</marks_possible>
                <feedback>Excellent work</feedback>
            </question>
            <question number="2">
                <marks_awarded>15</marks_awarded>
                <marks_possible>25</marks_possible>
                <feedback>Good effort</feedback>
            </question>
            <question number="3">
                <marks_awarded>22</marks_awarded>
                <marks_possible>25</marks_possible>
                <feedback>Very good</feedback>
            </question>
        </evaluation>`
    ];

    console.log('\nParsing XML with automatic validation:');
    
    // Parse the XML - this will automatically validate and correct totals
    const evaluationData = parseEvaluationData(sampleXML);
    
    if (evaluationData) {
        console.log(`Parsed Total: ${evaluationData.totalMarks}/${evaluationData.maxMarks} (${evaluationData.overallPercentage}%)`);
        console.log('Individual questions:');
        evaluationData.questions.forEach(q => {
            console.log(`  Question ${q.questionNumber}: ${q.marksAwarded}/${q.marksPossible} (${q.percentage}%)`);
        });
    }

    return evaluationData;
}

// Example 4: Usage in a React component context
export function exampleReactComponentUsage() {
    // This shows how you might use these functions in a React component
    const xmlData = ['<evaluation>...</evaluation>']; // Your XML data
    
    // Parse and get validated data
    const evaluationData = parseEvaluationData(xmlData);
    
    if (evaluationData) {
        // The totals are now guaranteed to be accurate
        const { totalMarks, maxMarks, overallPercentage } = evaluationData;
        
        // You can also manually verify if needed
        const calculatedTotal = calculateTotalMarksFromQuestions(evaluationData.questions);
        const calculatedMax = calculateTotalPossibleMarksFromQuestions(evaluationData.questions);
        
        console.log('\nReact Component Usage:');
        console.log(`Display Total: ${totalMarks}/${maxMarks} (${overallPercentage}%)`);
        console.log(`Verification: ${calculatedTotal}/${calculatedMax}`);
        console.log(`Totals match: ${totalMarks === calculatedTotal && maxMarks === calculatedMax}`);
        
        return {
            totalMarks,
            maxMarks,
            overallPercentage,
            isValid: totalMarks === calculatedTotal && maxMarks === calculatedMax
        };
    }
    
    return null;
}

// Run all examples
export function runAllExamples() {
    console.log('=== Total Marks Calculation Examples ===\n');
    
    exampleCalculateTotalMarks();
    exampleValidateEvaluationData();
    exampleParseAndValidateXML();
    exampleReactComponentUsage();
    
    console.log('\n=== Examples completed ===');
}
