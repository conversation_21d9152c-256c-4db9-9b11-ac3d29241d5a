import { useState, useEffect, useRef } from 'react';

interface ProgressData {
    progress: number;
    timestamp?: string;
}

interface UseProgressTrackingOptions {
    enabled?: boolean;
    onProgressUpdate?: (progress: number) => void;
    onError?: (error: Error) => void;
}

export const useProgressTracking = (
    jobId: string | null,
    options: UseProgressTrackingOptions = {}
) => {
    const [progress, setProgress] = useState<number>(0);
    const [isConnected, setIsConnected] = useState<boolean>(false);
    const [error, setError] = useState<Error | null>(null);
    const eventSourceRef = useRef<EventSource | null>(null);
    const { enabled = true, onProgressUpdate, onError } = options;

    useEffect(() => {
        if (!jobId || !enabled) {
            return;
        }

        const connectToProgressStream = () => {
            try {
                // Close existing connection if any
                if (eventSourceRef.current) {
                    eventSourceRef.current.close();
                }

                const eventSource = new EventSource(
                    `/api/aegisGrader/getCurrentProgress/${jobId}`
                );
                eventSourceRef.current = eventSource;

                eventSource.onopen = () => {
                    console.log('[Progress] Connected to progress stream for jobId:', jobId);
                    setIsConnected(true);
                    setError(null);
                };

                eventSource.onmessage = (event) => {
                    try {
                        const progressValue = JSON.parse(event.data);
                        console.log('[Progress] Received progress update:', progressValue);
                        
                        if (typeof progressValue === 'number') {
                            setProgress(progressValue);
                            onProgressUpdate?.(progressValue);
                        }
                    } catch (parseError) {
                        console.error('[Progress] Error parsing progress data:', parseError);
                        const error = new Error('Failed to parse progress data');
                        setError(error);
                        onError?.(error);
                    }
                };

                eventSource.onerror = (event) => {
                    console.error('[Progress] EventSource error:', event);
                    setIsConnected(false);
                    
                    const error = new Error('Connection to progress stream failed');
                    setError(error);
                    onError?.(error);

                    // Attempt to reconnect after a delay
                    setTimeout(() => {
                        if (eventSourceRef.current?.readyState === EventSource.CLOSED) {
                            console.log('[Progress] Attempting to reconnect...');
                            connectToProgressStream();
                        }
                    }, 5000);
                };

            } catch (connectionError) {
                console.error('[Progress] Error establishing connection:', connectionError);
                const error = connectionError instanceof Error 
                    ? connectionError 
                    : new Error('Failed to establish progress connection');
                setError(error);
                onError?.(error);
            }
        };

        connectToProgressStream();

        // Cleanup function
        return () => {
            if (eventSourceRef.current) {
                console.log('[Progress] Closing progress stream connection');
                eventSourceRef.current.close();
                eventSourceRef.current = null;
            }
            setIsConnected(false);
        };
    }, [jobId, enabled, onProgressUpdate, onError]);

    const disconnect = () => {
        if (eventSourceRef.current) {
            eventSourceRef.current.close();
            eventSourceRef.current = null;
            setIsConnected(false);
        }
    };

    return {
        progress,
        isConnected,
        error,
        disconnect
    };
};
